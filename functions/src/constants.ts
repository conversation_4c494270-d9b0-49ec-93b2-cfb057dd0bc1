/**
 * Marketplace Constants
 *
 * This file contains all static fee values and thresholds used throughout the marketplace functions.
 * These constants help maintain consistency and make it easier to update values in one place.
 */

// Order-related constants
export const SELLER_LOCK_PERCENTAGE = 0.2; // 20% of order amount locked by seller
export const BUYER_LOCK_PERCENTAGE = 1.0; // 100% of order amount locked by buyer
export const FIXED_REJECTION_FEE_TON = 0.1; // Fixed 0.1 TON fee for seller rejecting order with no buyer

// Transaction monitoring constants
export const MIN_TRANSACTION_THRESHOLD_TON = 0.9; // Minimum transaction amount to process (> 0.9 TON)

// Default fee values in BPS (basis points) - used as fallbacks
export const DEFAULT_REJECT_ORDER_FEE_BPS = 1000; // 10% default rejection fee

// Fee calculation constants
export const BPS_DIVISOR = 10000; // 1 BPS = 0.01%, so divide by 10000 to get decimal
export const PENALTY_SPLIT_RATIO = 0.5; // 50% split for penalties between marketplace and counterparty

// Collection and user constants
export const MARKETPLACE_REVENUE_USER_ID = "marketplace_revenue";
export const APP_CONFIG_COLLECTION = "app_config";
export const APP_CONFIG_DOC_ID = "fees";

// Conversion constants
export const NANOTONS_PER_TON = 1000000000; // 1 TON = 1,000,000,000 nanotons
