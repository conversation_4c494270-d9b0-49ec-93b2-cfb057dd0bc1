"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { loadFeesConfig, updateFeesConfig } from "@/api/fees-api";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, DollarSign } from "lucide-react";

// Fee configuration schema - deposit/withdraw are static TON values, others are BPS
const feesSchema = z.object({
  depositFee: z.number().min(0).max(10), // Static TON value
  withdrawFee: z.number().min(0).max(10), // Static TON value
  referrer_fee: z.number().min(0).max(10000), // BPS
  reject_order_fee: z.number().min(0).max(10000), // BPS
  purchase_fee: z.number().min(0).max(10000), // BPS
  buyer_lock_percentage: z.number().min(0).max(1), // Percentage (0.0-1.0)
  min_withdrawal_amount: z.number().min(0).max(1000), // Static TON value
  max_withdrawal_amount: z.number().min(0).max(10000), // Static TON value
});

type FeesFormData = z.infer<typeof feesSchema>;

export const FeesManagement = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const form = useForm<FeesFormData>({
    resolver: zodResolver(feesSchema),
    defaultValues: {
      depositFee: 0.1, // 0.1 TON default
      withdrawFee: 0.1, // 0.1 TON default
      referrer_fee: 200, // 2% BPS
      reject_order_fee: 150, // 1.5% BPS
      purchase_fee: 750, // 7.5% BPS
      buyer_lock_percentage: 1.0, // 100% default
      min_withdrawal_amount: 1, // 1 TON default
      max_withdrawal_amount: 100, // 100 TON default
    },
  });

  // Load current fees configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoadingData(true);
        const config = await loadFeesConfig();

        if (config) {
          form.reset({
            depositFee: config.depositFee || 0.1, // Default 0.1 TON
            withdrawFee: config.withdrawFee || 0.1, // Default 0.1 TON
            referrer_fee: config.referrer_fee || 200, // Default 2% BPS
            reject_order_fee: config.reject_order_fee || 150, // Default 1.5% BPS
            purchase_fee: config.purchase_fee || 750, // Default 7.5% BPS
            buyer_lock_percentage: config.buyer_lock_percentage || 1.0, // Default 100%
            min_withdrawal_amount: config.min_withdrawal_amount || 1, // Default 1 TON
            max_withdrawal_amount: config.max_withdrawal_amount || 100, // Default 100 TON
          });
        }
      } catch (error) {
        console.error("Error loading fees config:", error);
        toast({
          title: "Error",
          description: "Failed to load current fees configuration.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingData(false);
      }
    };

    loadConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSubmit = async (data: FeesFormData) => {
    setIsLoading(true);

    try {
      // All fees are now configurable
      await updateFeesConfig(data);

      toast({
        title: "Success",
        description: "Fees configuration has been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating fees config:", error);
      toast({
        title: "Error",
        description: "Failed to update fees configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatBpsToPercent = (bps: number) => {
    return (bps / 100).toFixed(2);
  };

  const formatTonValue = (ton: number) => {
    return ton.toFixed(4);
  };

  if (isLoadingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Fees Management
          </CardTitle>
          <CardDescription>
            Configure marketplace fees and referral rates
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading fees configuration...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <DollarSign className="h-4 w-4" />
          Fees Management
        </CardTitle>
        <CardDescription className="text-sm">
          Configure marketplace fees. Deposit/Withdraw are static TON values.
          Buyer lock percentage is 0.0-1.0. Others are BPS (100 BPS = 1%).
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="depositFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Deposit Fee (TON)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.1"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Static fee deducted from deposits. Current:{" "}
                    {formatTonValue(field.value)} TON
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="withdrawFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Withdraw Fee (TON)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.1"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Static fee deducted from withdrawals. Current:{" "}
                    {formatTonValue(field.value)} TON
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="min_withdrawal_amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Withdrawal Amount (TON)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Minimum amount users can withdraw. Current:{" "}
                    {formatTonValue(field.value)} TON
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_withdrawal_amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Maximum Withdrawal Amount (TON)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="100"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Maximum amount users can withdraw. Current:{" "}
                    {formatTonValue(field.value)} TON
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="purchase_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purchase Fee (BPS)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="750"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Total fee applied to purchases. Current:{" "}
                    {formatBpsToPercent(field.value)}%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="referrer_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referrer Fee (BPS)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="200"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Part of purchase fee given to referrer. Current:{" "}
                    {formatBpsToPercent(field.value)}%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reject_order_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Order Rejection Fee (BPS)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="150"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Fee applied when orders are rejected. Current:{" "}
                    {formatBpsToPercent(field.value)}%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="buyer_lock_percentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Buyer Lock Percentage</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      placeholder="1.0"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Percentage of order amount locked by buyer (0.0-1.0).
                    Current: {(field.value * 100).toFixed(0)}%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="bg-muted/50 p-3 rounded-lg">
              <h4 className="font-medium mb-2 text-sm">
                Fee Examples & Limits:
              </h4>
              <div className="text-xs space-y-1">
                <div>
                  • Deposit 10 TON → Fee:{" "}
                  {formatTonValue(form.watch("depositFee"))} TON → Net:{" "}
                  {(10 - form.watch("depositFee")).toFixed(4)} TON
                </div>
                <div>
                  • Withdraw 5 TON → Fee:{" "}
                  {formatTonValue(form.watch("withdrawFee"))} TON → Net:{" "}
                  {(5 - form.watch("withdrawFee")).toFixed(4)} TON
                </div>
                <div>
                  • Withdrawal Limits:{" "}
                  {formatTonValue(form.watch("min_withdrawal_amount"))} TON -{" "}
                  {formatTonValue(form.watch("max_withdrawal_amount"))} TON
                </div>
                <div>
                  • Purchase 100 TON → Fee:{" "}
                  {formatBpsToPercent(form.watch("purchase_fee"))}% ={" "}
                  {((100 * form.watch("purchase_fee")) / 10000).toFixed(2)} TON
                </div>
                <div>
                  • Buyer Lock:{" "}
                  {(form.watch("buyer_lock_percentage") * 100).toFixed(0)}% of
                  order amount
                </div>
              </div>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Fees...
                </>
              ) : (
                "Update Fees Configuration"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
