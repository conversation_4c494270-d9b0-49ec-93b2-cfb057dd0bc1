import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { OrderEntity } from "./types";
import {
  hasAvailableBalance,
  lockFunds,
  unlockFunds,
  spendLockedFunds,
  updateUserBalance,
} from "./balance-service";
import {
  applyPurchaseFeeWithReferral,
  getAppConfig,
  applyFeeToMarketplaceRevenue,
} from "./fee-service";
import { getNextCounterValue } from "./counter-service";
import { verifyBotToken } from "./bot-auth-service";
import {
  SELLER_LOCK_PERCENTAGE,
  FIXED_REJECTION_FEE_TON,
  DEFAULT_REJECT_ORDER_FEE_BPS,
  BPS_DIVISOR,
  BUYER_LOCK_PERCENTAGE,
} from "./constants";

export const createOrder = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { sellerId, productId, collectionId, amount } = data;

  if (!sellerId || !productId || !collectionId || !amount || amount <= 0) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "sellerId, productId, collectionId, and valid amount are required."
    );
  }

  if (context.auth.uid !== sellerId) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "You can only create orders for yourself as seller."
    );
  }

  try {
    const db = admin.firestore();

    // Get collection and validate floor price
    const collectionDoc = await db
      .collection("collections")
      .doc(collectionId)
      .get();
    if (!collectionDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "Collection not found."
      );
    }

    const collection = { id: collectionDoc.id, ...collectionDoc.data() } as any;
    if (collection.floorPrice && amount < collection.floorPrice) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Order amount ${amount} TON is below collection floor price of ${collection.floorPrice} TON.`
      );
    }

    // Check seller balance for 20% of order amount (new logic)
    const lockedAmount = amount * SELLER_LOCK_PERCENTAGE;
    const hasBalance = await hasAvailableBalance(sellerId, lockedAmount);
    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Insufficient available balance to create order. Need ${lockedAmount} TON (${
          SELLER_LOCK_PERCENTAGE * 100
        }% of order amount).`
      );
    }

    // Lock seller percentage of order amount for seller
    await lockFunds(sellerId, lockedAmount);

    // Get next order number
    const orderNumber = await getNextCounterValue("order_number");

    const orderData: Omit<OrderEntity, "id"> = {
      number: orderNumber,
      sellerId,
      productId,
      collectionId,
      amount,
      status: "active",
      createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
    };

    const orderRef = await db.collection("orders").add(orderData);

    return {
      success: true,
      orderId: orderRef.id,
      message: `Order created successfully with ${lockedAmount} TON locked (${
        SELLER_LOCK_PERCENTAGE * 100
      }% of ${amount} TON order)`,
    };
  } catch (error) {
    console.error("Error creating order:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchase = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const db = admin.firestore();
    const buyerId = context.auth.uid;

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status
    if (order.status !== "active") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order is not available for purchase."
      );
    }

    // Check if buyer is not the seller
    if (buyerId === order.sellerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You cannot purchase your own order."
      );
    }

    // Check if buyer has sufficient balance
    const hasBalance = await hasAvailableBalance(
      buyerId,
      order.amount * BUYER_LOCK_PERCENTAGE
    );
    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Insufficient available balance to make purchase."
      );
    }

    // Lock buyer's funds (fee will be applied when purchase is completed)
    await lockFunds(buyerId, order.amount);

    // Update order status and add buyer
    await db.collection("orders").doc(orderId).update({
      buyerId,
      status: "paid",
    });

    return {
      success: true,
      message: `Purchase completed successfully for ${order.amount} TON`,
    };
  } catch (error) {
    console.error("Error making purchase:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});

export const rejectPurchase = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = context.auth.uid;

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if order can be rejected (active or paid status)
    if (order.status !== "active" && order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order cannot be rejected in current status."
      );
    }

    // Check if user is either buyer or seller
    const isSeller = userId === order.sellerId;
    const isBuyer = order.buyerId && userId === order.buyerId;

    if (!isSeller && !isBuyer) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only buyer or seller can reject the order."
      );
    }

    const sellerLockedAmount = order.amount * SELLER_LOCK_PERCENTAGE;

    // Case 1: Seller rejects order with no buyer (order status is "active")
    if (isSeller && order.status === "active" && !order.buyerId) {
      // Apply fixed rejection fee to seller
      const fixedFee = FIXED_REJECTION_FEE_TON;
      await updateUserBalance(userId, -fixedFee, 0);
      await applyFeeToMarketplaceRevenue(
        fixedFee,
        "reject_order_spam_protection"
      );

      // Unlock seller's funds
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Delete the order
      await db.collection("orders").doc(orderId).delete();

      return {
        success: true,
        message: `Order deleted successfully. Fixed fee of ${fixedFee} TON applied for spam protection.`,
      };
    }

    // Case 2: Seller rejects order with buyer (order status is "paid")
    if (isSeller && order.status === "paid" && order.buyerId) {
      const config = await getAppConfig();
      const rejectFeePercentage =
        config?.reject_order_fee ?? DEFAULT_REJECT_ORDER_FEE_BPS;

      // Seller loses their locked amount (20% of order)
      // From the order amount: reject_order_fee% goes to marketplace, remaining goes to buyer
      const marketplaceFee = (order.amount * rejectFeePercentage) / BPS_DIVISOR;
      const buyerCompensation = order.amount - marketplaceFee;

      // Transfer funds
      await spendLockedFunds(order.sellerId, sellerLockedAmount); // Seller loses their 20%
      await updateUserBalance(order.buyerId, buyerCompensation, 0); // Buyer gets order amount - marketplace fee
      await applyFeeToMarketplaceRevenue(
        marketplaceFee,
        "reject_order_penalty"
      );

      // Unlock buyer's funds (they get their money back + compensation)
      await unlockFunds(order.buyerId, order.amount);

      // Delete the order
      await db.collection("orders").doc(orderId).delete();

      return {
        success: true,
        message: `Order rejected. Seller lost ${sellerLockedAmount} TON. Buyer received ${buyerCompensation} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
      };
    }

    // Case 3: Buyer rejects order
    if (isBuyer) {
      const config = await getAppConfig();
      const rejectFeePercentage =
        config?.reject_order_fee ?? DEFAULT_REJECT_ORDER_FEE_BPS;

      // Buyer loses their locked amount (100% of order)
      // From the order amount: reject_order_fee% goes to marketplace, remaining goes to seller
      const marketplaceFee = (order.amount * rejectFeePercentage) / BPS_DIVISOR;
      const sellerCompensation = order.amount - marketplaceFee;

      // Transfer funds
      if (order.buyerId) {
        await spendLockedFunds(order.buyerId, order.amount); // Buyer loses their 100%
      }

      await updateUserBalance(order.sellerId, sellerCompensation, 0); // Seller gets order amount - marketplace fee
      await applyFeeToMarketplaceRevenue(
        marketplaceFee,
        "reject_order_penalty"
      );

      // Unlock seller's funds (they get their money back + compensation)
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Delete the order
      await db.collection("orders").doc(orderId).delete();

      return {
        success: true,
        message: `Order rejected. Buyer lost ${order.amount} TON. Seller received ${sellerCompensation} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
      };
    }

    // Fallback (should not reach here)
    throw new functions.https.HttpsError(
      "internal",
      "Invalid rejection scenario."
    );
  } catch (error) {
    console.error("Error rejecting purchase:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while rejecting purchase."
    );
  }
});

export const completePurchase = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check order status - must be paid to complete
      if (order.status !== "paid") {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order must be in 'paid' status to complete."
        );
      }

      // Check if order has a buyer
      if (!order.buyerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order has no buyer."
        );
      }

      // Get buyer's referral information
      const buyerDoc = await db.collection("users").doc(order.buyerId).get();
      const buyerData = buyerDoc.data();
      const referralId = buyerData?.referral_id;

      // Apply purchase fee with referral logic
      const feeResult = await applyPurchaseFeeWithReferral(
        order.buyerId,
        order.amount,
        referralId
      );
      const netAmountToSeller = order.amount - feeResult.totalFee;

      // Spend buyer's locked funds (removes from both sum and locked)
      await spendLockedFunds(order.buyerId, order.amount);

      // Unlock seller's funds
      const sellerLockedAmount = order.amount * SELLER_LOCK_PERCENTAGE;
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Transfer net amount to seller
      await updateUserBalance(order.sellerId, netAmountToSeller, 0);

      // Update order status to fulfilled
      await db.collection("orders").doc(orderId).update({
        status: "fulfilled",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase completed successfully. Seller received ${netAmountToSeller} TON (${feeResult.totalFee} TON fee applied, ${feeResult.referralFee} TON to referrer)`,
        netAmountToSeller,
        feeAmount: feeResult.totalFee,
        referralFee: feeResult.referralFee,
        marketplaceFee: feeResult.marketplaceFee,
      };
    } catch (error) {
      console.error("Error completing purchase:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while completing purchase."
      );
    }
  }
);

export const getOrdersByProductId = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { productId } = data;

    if (!productId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Product ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Query orders by productId with status "paid" (ready for completion)
      const ordersQuery = await db
        .collection("orders")
        .where("productId", "==", productId)
        .where("status", "==", "paid")
        .get();

      const orders: OrderEntity[] = [];
      ordersQuery.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
      });

      return {
        success: true,
        orders,
        count: orders.length,
      };
    } catch (error) {
      console.error("Error getting orders by product ID:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while getting orders."
      );
    }
  }
);

export const getUserOrders = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { userId, tgId } = data;
  const requesterId = context.auth.uid;

  try {
    const db = admin.firestore();
    let targetUserId = userId;

    // If tgId is provided, find user by Telegram ID
    if (tgId && !userId) {
      const userQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId.toString())
        .limit(1)
        .get();

      if (userQuery.empty) {
        throw new functions.https.HttpsError(
          "not-found",
          "User not found with provided Telegram ID."
        );
      }

      targetUserId = userQuery.docs[0].id;
    }

    // If no userId or tgId provided, use the authenticated user's ID
    if (!targetUserId) {
      targetUserId = requesterId;
    }

    // Check if requester has permission to view these orders
    // Users can only view their own orders unless they're admin
    if (targetUserId !== requesterId) {
      const requesterDoc = await db.collection("users").doc(requesterId).get();
      const requesterData = requesterDoc.data();

      if (!requesterData || requesterData.role !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "You can only view your own orders."
        );
      }
    }

    // Get orders where user is either buyer or seller
    const [sellerOrdersQuery, buyerOrdersQuery] = await Promise.all([
      db.collection("orders").where("sellerId", "==", targetUserId).get(),
      db.collection("orders").where("buyerId", "==", targetUserId).get(),
    ]);

    const orders: OrderEntity[] = [];
    const orderIds = new Set<string>();

    // Add seller orders
    sellerOrdersQuery.forEach((doc) => {
      if (!orderIds.has(doc.id)) {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
        orderIds.add(doc.id);
      }
    });

    // Add buyer orders (avoid duplicates)
    buyerOrdersQuery.forEach((doc) => {
      if (!orderIds.has(doc.id)) {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
        orderIds.add(doc.id);
      }
    });

    // Sort orders by creation date (newest first)
    orders.sort((a, b) => {
      const aTime = a.createdAt?.toMillis() || 0;
      const bTime = b.createdAt?.toMillis() || 0;
      return bTime - aTime;
    });

    return {
      success: true,
      orders,
      count: orders.length,
      userId: targetUserId,
    };
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user orders."
    );
  }
});

export const completePurchaseByBot = functions.https.onCall(async (data) => {
  const { orderId, botToken } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  if (!botToken) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Bot token is required."
    );
  }

  // Verify bot token
  if (!verifyBotToken(botToken)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Invalid bot token."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status - must be paid to complete
    if (order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order must be in 'paid' status to complete."
      );
    }

    // Check if order has a buyer
    if (!order.buyerId) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order has no buyer."
      );
    }

    // Get buyer's referral information
    const buyerDoc = await db.collection("users").doc(order.buyerId).get();
    const buyerData = buyerDoc.data();
    const referralId = buyerData?.referral_id;

    // Apply purchase fee with referral logic
    const feeResult = await applyPurchaseFeeWithReferral(
      order.buyerId,
      order.amount,
      referralId
    );
    const netAmountToSeller = order.amount - feeResult.totalFee;

    // Spend buyer's locked funds (removes from both sum and locked)
    await spendLockedFunds(order.buyerId, order.amount);

    // Unlock seller's funds
    await unlockFunds(order.sellerId, order.amount);

    // Transfer net amount to seller
    await updateUserBalance(order.sellerId, netAmountToSeller, 0);

    // Update order status to fulfilled
    await db.collection("orders").doc(orderId).update({
      status: "fulfilled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Purchase completed successfully by bot. Seller received ${netAmountToSeller} TON (${feeResult.totalFee} TON fee applied, ${feeResult.referralFee} TON to referrer)`,
      netAmountToSeller,
      feeAmount: feeResult.totalFee,
      referralFee: feeResult.referralFee,
      marketplaceFee: feeResult.marketplaceFee,
      order: {
        id: order.id,
        number: order.number,
        status: "fulfilled",
      },
    };
  } catch (error) {
    console.error("Error completing purchase by bot:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while completing purchase."
    );
  }
});
